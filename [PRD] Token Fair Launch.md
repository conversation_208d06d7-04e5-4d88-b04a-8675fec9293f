# \[PRD\] Token Fair Launch

# 业务目标

1 通过众筹模式为项目筹集资金  
2 帮助项目方建立早期忠实Token Holders群体

# 用户画像

**1** 对Token投资感兴趣的Token投资者  
**2** 寻求投资机会的DeFi参与者  
**用户特征：** 具备基础的Web3知识，有wallet，对新项目感兴趣

# 用户场景

#### **1 新用户首次购买**

Token投资者想要参与ONTON Token的Pre-Launch投资，以获得fair launch的参与资格。

**用户体验流程**：了解项目信息 → 查看投资规则 →连接钱包 →决定购买数量 → 完成购买

#### **2 已购买用户追加投资**

已购买用户想要增加投资金额，以获得更多Token份额。

**用户体验流程：**查看当前持有量 →评估剩余额度→追加购买

# 产品方案

构建一个专为Token预售设计的、具有强烈紧迫感的Token Launch Pad。  
ONION Token Fair Launch 采用英式拍卖机制，限时竞拍的方式，提供透明高效的代币发行流程。  
产品需要支持完整的Token购买流程和便捷的退款流程。  
Launch Pad页面实时显示Token筹集进度条、倒计时器、Token分配比例等关键信息，可以通过动态更新的价格动态、带有紧急感文案、进度变色提示营造强烈的FOMO氛围，吸引用户购买。

## Fair Launch的不同情况

根据时间，Fair Launch分为3个状态：Purchase未开始，Purchase进行中，Purchase已结束

**1 Purchase未开始**

**2 Purchase进行中：**  
2.1 Token申购数量\<100%  
用户可以填写50U以上任意数值申购。  
根据对应的轮次和单价，计算应该获得的token份额。  
若用户应获得的token份额\<当前Token剩余量，则申购成功。  
若用户应获得的token份额\>当前Token剩余量，提示超额，建议当前最大可申购数量，用户修改填入的数值。

2.2 Token申购数量=100%  
Fair Launch立即结束，Launch成功。

**3 Purchase已结束**  
若筹集资金达到SoftCap, Launch成功。  
若筹集资金未达到SoftCap, Launch失败。

## 产品功能模块

### 1 实时数据展示模块

1）已售Token数量: XXX ONIONs（实时更新）

2）筹集资金: $1.6M（实时更新）

3）拍卖进度: 67.3%（带可视化进度条）

没到开始时间 展示未开始

时间点在中间 展示进行中的状态 用户实际上能不能买根据后端剩余token数量判断

到结束时间 展示launch结果

4）剩余Token: XXX ONIONs remaining（实时更新）

### 

### 2 拍卖信息展示模块

#### **2.1 Auction基础信息展示**

1）Fair Launch Token总量: 1M ONIONs (占总供应量10%)

2）软顶: $0.5M

3）硬顶: $2M

4）开始时间: 2025-07-15 00:00 (UTC)

5）结束时间: 2025-07-18 00:00 (UTC)

**2.2 Auction规则说明**

1）英式拍卖机制: 价格随时间和需求动态调整

2）公平透明: 平等参与，公开竞拍

3）社区激励: 吸引长期支持者，提升社区参与度

4）去中心化: 降低代币集中度和内部交易风险

### 

### 3 购买模块设计

#### **3.1 购买界面设计**

**1） 倒计时系统及价格信息展示**

* 展示格式: XH XM XS（小时-分钟-秒）  
* 当前轮次价格: $0.10（Live Round Price）  
* 上一轮价格: $0.09（Last Round Price）  
* 价格变化趋势: 根据英式拍卖规则自动调整

**2）购买模块**

1) 输入框: 支持金额输入  
2) 币种选择: 支持USDT和TON。  
3) 实时计算: 根据当前Token Price，USDT或者TON的实时价格，自动计算可获得的ONION数量  
4) 购买按钮: "Purchase Now" 按钮

**3.2 购买流程**

1）前置条件检查

* 拍卖是否在进行中  
  * 钱包连接状态  
  * 剩余Token数量检查

2）购买执行流程

* 用户输入购买金额  
  * 系统根据当前轮次实时计算Token数量  
  * 用户确认交易详情  
  * 调用智能合约执行购买  
  * 交易状态跟踪和结果反馈

3）交易状态管理

* 排队中（Pending）  
  * 交易确认中（Confirming）  
  * 交易成功（Success）  
  * 交易失败（Failed）- 显示具体失败原因

### 4 Auction History模块

#### **拍卖轮次历史记录**

1）轮次信息，按照时间倒序排列: Round 9, Round 8, Round 7...

2）时间记录: 每轮的时间范围（按照UTC时间展示）

3）价格信息: 每轮的成交价格，美金为单位

4）成交量: 每轮的ONION成交数量

### 5 用户购买历史及退款功能模块

#### **5.1 用户购买历史**

1）购买记录: 显示用户所有购买记录

2）金额详情: 购买金额和获得的Token数量

3）退款功能: 支持在拍卖期间随时申请退款

4）状态跟踪: 购买状态、退款状态实时更新

#### 

#### **5.2 退款功能**

1）退款条件检查

* 拍卖仍在进行中（未结束）  
* 用户有有效的购买记录

2）退款流程

1. 用户在"购买历史"中查看订单  
2. 点击需要退款订单的“Refund”按钮  
3. 系统显示退款确认弹窗  
   * 退款金额明细  
   * 手续费说明: 按照认购金额的5%收取  
   * 风险提示  
4. 用户确认退款  
5. 系统执行退款操作。后端更新剩余可购买token数，前端Auction Process Bar不更新  
6. 退款状态跟踪和结果通知

## 

## 数据更新

1）Auction Process进度数据库实时更新，前端展示每2分钟更新一次

2）倒计时实时刷新

## 

## 其他

1）防止用户重复提交购买和退款请求  
2）多个用户同时触发购买时：  
用户点击购买后立即显示"处理中"状态，给出清晰的交易状态反馈：排队中 \- 交易确认中 \- 交易成功/失败；如果交易失败，需要明确告知用户失败原因（Token已售罄、余额不足、网络问题等）；  
及时更新剩余Token数量，避免用户看到过期信息。

