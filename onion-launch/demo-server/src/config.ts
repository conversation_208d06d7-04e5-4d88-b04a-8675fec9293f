import dotenv from 'dotenv';
import { ServerConfig } from './types';

dotenv.config();

export const config: ServerConfig = {
  port: parseInt(process.env.PORT || '3001'),
  signing_private_key: process.env.SIGNING_PRIVATE_KEY || '',
  signing_public_key: process.env.SIGNING_PUBLIC_KEY || '',
  auction_contract_address: process.env.AUCTION_CONTRACT_ADDRESS || '',
  allowed_origins: (process.env.ALLOWED_ORIGINS || 'http://localhost:3000').split(','),
};

// Validate required configuration
if (!config.signing_private_key) {
  throw new Error('SIGNING_PRIVATE_KEY is required');
}

if (!config.signing_public_key) {
  throw new Error('SIGNING_PUBLIC_KEY is required');
}

if (!config.auction_contract_address) {
  console.warn('AUCTION_CONTRACT_ADDRESS not set - using mock data');
}

export const auctionConfig = {
  round_duration: parseInt(process.env.ROUND_DURATION || '3600'), // 1 hour
  price_increment: BigInt(process.env.PRICE_INCREMENT || '10000000'), // 0.01 TON
  min_purchase: BigInt(process.env.MIN_PURCHASE || '50000000000'), // 50 TON
  initial_price: BigInt(process.env.INITIAL_PRICE || '100000000'), // 0.1 TON
  
  // Mock auction parameters for demo
  start_time: Math.floor(Date.now() / 1000) - 3600, // Started 1 hour ago
  end_time: Math.floor(Date.now() / 1000) + 86400, // Ends in 24 hours
  soft_cap: BigInt('500000000000000'), // 500k TON
  hard_cap: BigInt('2000000000000000'), // 2M TON
  total_supply: BigInt('1000000000000000'), // 1M tokens
};
