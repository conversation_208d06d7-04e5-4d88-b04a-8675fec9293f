import express from 'express';
import { TokenCalculator } from './calculator';
import { SignatureService } from './signer';
import { PurchaseRequest, PurchaseResponse } from './types';

const router = express.Router();
const calculator = new TokenCalculator();
const signer = new SignatureService();

/**
 * Health check endpoint
 */
router.get('/health', (req, res) => {
  res.json({ 
    status: 'ok', 
    timestamp: new Date().toISOString(),
    service: 'onion-auction-demo-server'
  });
});

/**
 * Get current auction state
 */
router.get('/auction/state', (req, res) => {
  try {
    const state = calculator.getCurrentAuctionState();
    
    // Convert BigInt to string for JSON serialization
    const response = {
      current_round: state.current_round,
      current_price: state.current_price.toString(),
      total_raised: state.total_raised.toString(),
      total_tokens_sold: state.total_tokens_sold.toString(),
      auction_status: state.auction_status,
      purchase_count: state.purchase_count
    };

    res.json(response);
  } catch (error) {
    console.error('Error getting auction state:', error);
    res.status(500).json({ 
      error: 'Failed to get auction state',
      message: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

/**
 * Calculate purchase and generate signature
 */
router.post('/purchase/calculate', (req, res) => {
  try {
    const { user_address, amount, currency }: PurchaseRequest = req.body;

    // Validate input
    if (!user_address || !amount || currency === undefined) {
      return res.status(400).json({
        success: false,
        error: 'Missing required fields: user_address, amount, currency'
      });
    }

    // Validate currency
    if (currency !== 0 && currency !== 1) {
      return res.status(400).json({
        success: false,
        error: 'Invalid currency. Use 0 for TON, 1 for USDT'
      });
    }

    // Parse amount
    const amountBigInt = BigInt(amount);
    if (amountBigInt <= 0) {
      return res.status(400).json({
        success: false,
        error: 'Amount must be positive'
      });
    }

    // Generate calculation
    const calculation = calculator.generatePurchaseCalculation(
      user_address,
      amountBigInt,
      currency
    );

    // Sign the calculation
    const signature = signer.signPurchaseCalculation(calculation);

    // Prepare response (convert BigInt to string for JSON)
    const response: PurchaseResponse = {
      calculation: {
        ...calculation,
        user: calculation.user,
        amount: calculation.amount,
        tokens_to_receive: calculation.tokens_to_receive,
        current_price: calculation.current_price,
        nonce: calculation.nonce
      },
      signature,
      success: true
    };

    res.json(response);
  } catch (error) {
    console.error('Error calculating purchase:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to calculate purchase',
      message: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

/**
 * Get server public key for contract configuration
 */
router.get('/config/public-key', (req, res) => {
  try {
    const publicKey = signer.getPublicKey();
    const publicKeyBigInt = signer.getPublicKeyBigInt();

    res.json({
      public_key_hex: publicKey,
      public_key_bigint: publicKeyBigInt.toString(),
      note: 'Use public_key_bigint for contract SetSigningKey message'
    });
  } catch (error) {
    console.error('Error getting public key:', error);
    res.status(500).json({
      error: 'Failed to get public key',
      message: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

/**
 * Verify signature (for testing)
 */
router.post('/signature/verify', (req, res) => {
  try {
    const { calculation, signature } = req.body;

    if (!calculation || !signature) {
      return res.status(400).json({
        error: 'Missing calculation or signature'
      });
    }

    // Convert string fields back to appropriate types
    const calc = {
      ...calculation,
      amount: BigInt(calculation.amount),
      tokens_to_receive: BigInt(calculation.tokens_to_receive),
      current_price: BigInt(calculation.current_price),
      nonce: BigInt(calculation.nonce)
    };

    const isValid = signer.verifySignature(calc, signature);

    res.json({
      valid: isValid,
      message: isValid ? 'Signature is valid' : 'Signature is invalid'
    });
  } catch (error) {
    console.error('Error verifying signature:', error);
    res.status(500).json({
      error: 'Failed to verify signature',
      message: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

export default router;
