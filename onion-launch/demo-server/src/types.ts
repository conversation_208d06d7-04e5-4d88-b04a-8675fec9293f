import { Address } from '@ton/core';

export interface PurchaseCalculation {
  user: Address;
  amount: bigint;
  currency: number; // 0=TON, 1=USDT
  tokens_to_receive: bigint;
  current_price: bigint;
  current_round: number;
  timestamp: number;
  nonce: bigint;
}

export interface PurchaseRequest {
  user_address: string;
  amount: string;
  currency: number; // 0=TON, 1=USDT
}

export interface PurchaseResponse {
  calculation: PurchaseCalculation;
  signature: string;
  success: boolean;
  error?: string;
}

export interface AuctionConfig {
  start_time: number;
  end_time: number;
  soft_cap: bigint;
  hard_cap: bigint;
  total_supply: bigint;
  initial_price: bigint;
  round_duration: number;
  price_increment: bigint;
  min_purchase: bigint;
}

export interface AuctionState {
  current_round: number;
  current_price: bigint;
  total_raised: bigint;
  total_tokens_sold: bigint;
  auction_status: number;
  purchase_count: number;
}

export interface ServerConfig {
  port: number;
  signing_private_key: string;
  signing_public_key: string;
  auction_contract_address: string;
  allowed_origins: string[];
}
