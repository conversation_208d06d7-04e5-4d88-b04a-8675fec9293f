#!/usr/bin/env ts-node

import { keyPairFromSeed } from '@ton/crypto';
import { randomBytes } from 'crypto';
import * as fs from 'fs';
import * as path from 'path';

/**
 * Generate a new key pair for signing
 */
function generateKeyPair() {
  // Generate random seed
  const seed = randomBytes(32);
  
  // Generate key pair
  const keyPair = keyPairFromSeed(seed);
  
  return {
    seed: seed.toString('hex'),
    privateKey: keyPair.secretKey.toString('hex'),
    publicKey: keyPair.publicKey.toString('hex'),
    publicKeyBigInt: BigInt('0x' + keyPair.publicKey.toString('hex')).toString()
  };
}

/**
 * Update .env file with new keys
 */
function updateEnvFile(keys: ReturnType<typeof generateKeyPair>) {
  const envPath = path.join(__dirname, '..', '.env');
  const envExamplePath = path.join(__dirname, '..', '.env.example');
  
  let envContent = '';
  
  // Read existing .env file or use .env.example as template
  if (fs.existsSync(envPath)) {
    envContent = fs.readFileSync(envPath, 'utf8');
    console.log('📝 Updating existing .env file...');
  } else if (fs.existsSync(envExamplePath)) {
    envContent = fs.readFileSync(envExamplePath, 'utf8');
    console.log('📝 Creating .env file from template...');
  } else {
    console.log('📝 Creating new .env file...');
    envContent = `# Server Configuration
PORT=3001
NODE_ENV=development

# Signing Key Configuration
SIGNING_PRIVATE_KEY=
SIGNING_PUBLIC_KEY=

# Auction Configuration
AUCTION_CONTRACT_ADDRESS=
ROUND_DURATION=3600
PRICE_INCREMENT=10000000
MIN_PURCHASE=50000000000
INITIAL_PRICE=100000000

# CORS Configuration
ALLOWED_ORIGINS=http://localhost:3000,http://localhost:3001
`;
  }
  
  // Update or add key configuration
  envContent = envContent.replace(
    /SIGNING_PRIVATE_KEY=.*/,
    `SIGNING_PRIVATE_KEY=${keys.seed}`
  );
  
  envContent = envContent.replace(
    /SIGNING_PUBLIC_KEY=.*/,
    `SIGNING_PUBLIC_KEY=${keys.publicKey}`
  );
  
  // Write updated content
  fs.writeFileSync(envPath, envContent);
  console.log('✅ .env file updated successfully');
}

/**
 * Save keys to a separate file for backup
 */
function saveKeysBackup(keys: ReturnType<typeof generateKeyPair>) {
  const backupDir = path.join(__dirname, '..', 'keys');
  const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
  const backupPath = path.join(backupDir, `keys-${timestamp}.json`);
  
  // Create keys directory if it doesn't exist
  if (!fs.existsSync(backupDir)) {
    fs.mkdirSync(backupDir, { recursive: true });
  }
  
  const keyData = {
    generated_at: new Date().toISOString(),
    seed: keys.seed,
    private_key: keys.privateKey,
    public_key: keys.publicKey,
    public_key_bigint: keys.publicKeyBigInt,
    note: 'Keep this file secure and never share the private key or seed'
  };
  
  fs.writeFileSync(backupPath, JSON.stringify(keyData, null, 2));
  console.log(`💾 Keys backed up to: ${backupPath}`);
}

/**
 * Main function
 */
function main() {
  console.log('🔐 Generating new signing key pair...\n');
  
  try {
    // Generate keys
    const keys = generateKeyPair();
    
    // Display keys
    console.log('Generated Keys:');
    console.log('==============');
    console.log(`Seed (for SIGNING_PRIVATE_KEY):     ${keys.seed}`);
    console.log(`Public Key (hex):                   ${keys.publicKey}`);
    console.log(`Public Key (BigInt for contract):   ${keys.publicKeyBigInt}`);
    console.log('');
    
    // Update .env file
    updateEnvFile(keys);
    
    // Save backup
    saveKeysBackup(keys);
    
    console.log('\n🎉 Key generation completed!');
    console.log('\n📋 Next steps:');
    console.log('1. Start the demo server: npm run dev');
    console.log('2. Get the public key: GET /api/config/public-key');
    console.log('3. Set the public key in your smart contract using SetSigningKey message');
    console.log(`4. Use this BigInt value: ${keys.publicKeyBigInt}`);
    console.log('\n⚠️  Security Warning:');
    console.log('- Keep the seed/private key secure and never share it');
    console.log('- The keys backup is saved in the keys/ directory');
    console.log('- Add keys/ directory to .gitignore to prevent accidental commits');
    
  } catch (error) {
    console.error('❌ Error generating keys:', error);
    process.exit(1);
  }
}

// Run if called directly
if (require.main === module) {
  main();
}

export { generateKeyPair, updateEnvFile, saveKeysBackup };
