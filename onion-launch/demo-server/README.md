# Onion Auction Demo Server

链下 token 计算和签名生成服务，用于支持 OnionAuction 智能合约的签名验证功能。

## 功能特性

- **链下计算**: 实现与智能合约相同的 token 计算逻辑
- **签名生成**: 使用私钥对计算结果进行签名
- **防重放攻击**: 使用 nonce 机制防止重放攻击
- **实时价格**: 基于时间的动态价格计算
- **多币种支持**: 支持 TON 和 USDT 购买

## 快速开始

### 1. 安装依赖

```bash
npm install
```

### 2. 配置环境变量

复制 `.env.example` 到 `.env` 并配置：

```bash
cp .env.example .env
```

编辑 `.env` 文件，设置必要的配置项。

### 3. 生成签名密钥对

```bash
npm run generate-keys
```

### 4. 启动开发服务器

```bash
npm run dev
```

### 5. 构建生产版本

```bash
npm run build
npm start
```

## API 接口

### 健康检查
```
GET /api/health
```

### 获取拍卖状态
```
GET /api/auction/state
```

### 计算购买并生成签名
```
POST /api/purchase/calculate
Content-Type: application/json

{
  "user_address": "EQD...",
  "amount": "100000000000",
  "currency": 0
}
```

### 获取公钥
```
GET /api/config/public-key
```

### 验证签名（测试用）
```
POST /api/signature/verify
Content-Type: application/json

{
  "calculation": { ... },
  "signature": "..."
}
```

## 配置说明

| 环境变量 | 说明 | 默认值 |
|---------|------|--------|
| PORT | 服务器端口 | 3001 |
| SIGNING_PRIVATE_KEY | 签名私钥 | 必填 |
| SIGNING_PUBLIC_KEY | 签名公钥 | 必填 |
| AUCTION_CONTRACT_ADDRESS | 拍卖合约地址 | 可选 |
| ROUND_DURATION | 轮次持续时间（秒） | 3600 |
| PRICE_INCREMENT | 价格增量 | 10000000 |
| MIN_PURCHASE | 最小购买金额 | 50000000000 |
| INITIAL_PRICE | 初始价格 | 100000000 |

## 安全注意事项

1. **私钥管理**: 生产环境中请使用安全的密钥管理系统
2. **HTTPS**: 生产环境中必须使用 HTTPS
3. **CORS**: 正确配置 CORS 允许的域名
4. **速率限制**: 建议添加 API 速率限制
5. **日志记录**: 记录所有签名生成操作用于审计

## 开发指南

### 项目结构

```
src/
├── index.ts          # 服务器入口
├── config.ts         # 配置管理
├── types.ts          # 类型定义
├── calculator.ts     # Token 计算逻辑
├── signer.ts         # 签名服务
└── routes.ts         # API 路由
```

### 测试

```bash
npm test
```

### 代码格式化

```bash
npm run format
```

## 部署

### Docker 部署

```bash
docker build -t onion-auction-server .
docker run -p 3001:3001 --env-file .env onion-auction-server
```

### PM2 部署

```bash
npm install -g pm2
npm run build
pm2 start dist/index.js --name onion-auction-server
```

## 故障排除

### 常见问题

1. **签名验证失败**: 检查私钥配置和数据格式
2. **计算结果不匹配**: 确保计算参数与合约一致
3. **CORS 错误**: 检查 ALLOWED_ORIGINS 配置

### 日志查看

开发环境：
```bash
npm run dev
```

生产环境：
```bash
pm2 logs onion-auction-server
```
