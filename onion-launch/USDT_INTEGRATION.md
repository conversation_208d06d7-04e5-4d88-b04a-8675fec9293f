# OnionAuction USDT 集成指南

## 概述

OnionAuction 合约现在支持使用 USDT（Jetton 标准代币）进行购买，除了原有的 TON 支持。这个功能遵循 TON 区块链的 TEP-74 Jetton 标准。

## 新增功能

### 1. USDT 配置

合约所有者可以设置 USDT 支持：

```typescript
// 设置 USDT 配置
await onionAuction.send(
    owner.getSender(),
    { value: toNano('0.1') },
    {
        $$type: 'SetUSDTAddress',
        usdt_master: Address.parse('EQBynBO23ywHy_CgarY9NK9FTz0yDsG82PtcbSTQgGoXwiuA'), // USDT Master 合约地址
        usdt_wallet: Address.parse('EQXXX...') // 拍卖合约的 USDT 钱包地址
    }
);
```

### 2. USDT 购买

用户可以通过发送 USDT 到拍卖合约来购买代币。这通过 Jetton 转账通知机制实现：

```typescript
// 用户通过 USDT 钱包发送 USDT
await usdtWallet.send(
    user.getSender(),
    { value: to<PERSON>ano('0.1') },
    {
        $$type: 'JettonTransfer',
        query_id: 0n,
        amount: 1000000n, // 1 USDT (6 decimals)
        destination: auctionAddress,
        response_destination: userAddress,
        custom_payload: null,
        forward_ton_amount: 1n, // 1 nanoton for notification
        forward_payload: beginCell().endCell()
    }
);
```

### 3. 价格计算

- USDT 和 TON 使用相同的代币价格
- USDT 有 6 位小数，TON 有 9 位小数
- 合约自动处理小数位转换：`usdt_amount * 1000 = ton_equivalent`

### 4. 退款支持

支持 USDT 退款，通过 Jetton 转账实现：

```typescript
// 用户请求退款（在 UserPurchase 合约中）
await userPurchase.send(
    user.getSender(),
    { value: toNano('0.1') },
    {
        $$type: 'Refund',
        purchase_id: 1n
    }
);
```

## 合约结构变更

### 新增消息类型

1. **SetUSDTAddress** - 设置 USDT 配置
2. **JettonTransferNotification** - 处理 USDT 转账通知

### 新增状态变量

1. **usdt_config** - USDT 配置信息
2. **total_raised_usdt** - USDT 总筹集金额

### 新增 Getter 方法

1. **usdt_config()** - 获取 USDT 配置
2. **total_raised_usdt()** - 获取 USDT 筹集总额
3. **total_raised_equivalent()** - 获取等价 TON 总筹集额
4. **is_usdt_enabled()** - 检查是否启用 USDT

## 安全考虑

1. **地址验证** - 合约验证 USDT 转账来自正确的 Jetton 钱包
2. **主合约验证** - 验证 Jetton 钱包属于正确的 USDT 主合约
3. **最小购买金额** - USDT 购买也需要满足最小购买金额要求
4. **退款费用** - USDT 退款同样收取 5% 手续费

## 部署步骤

1. 部署 OnionAuction 合约
2. 获取合约的 USDT 钱包地址（通过调用 USDT 主合约的 get_wallet_address 方法）
3. 调用 SetUSDTAddress 设置 USDT 配置
4. 开始拍卖

## 测试示例

```typescript
describe('OnionAuction USDT Integration', () => {
    it('should handle USDT purchases', async () => {
        // 1. 设置 USDT 配置
        await onionAuction.send(owner.getSender(), 
            { value: toNano('0.1') },
            {
                $$type: 'SetUSDTAddress',
                usdt_master: usdtMasterAddress,
                usdt_wallet: usdtWalletAddress
            }
        );

        // 2. 开始拍卖
        await onionAuction.send(owner.getSender(),
            { value: toNano('0.1') },
            {
                $$type: 'StartAuction',
                start_time: BigInt(Math.floor(Date.now() / 1000)),
                end_time: BigInt(Math.floor(Date.now() / 1000)) + 86400n,
                soft_cap: toNano('500000'),
                hard_cap: toNano('2000000'),
                initial_price: toNano('0.1')
            }
        );

        // 3. 模拟 USDT 转账通知
        await onionAuction.send(usdtWallet.getSender(),
            { value: toNano('0.1') },
            {
                $$type: 'JettonTransferNotification',
                query_id: 0n,
                amount: 50000000n, // 50 USDT
                sender: buyer.address,
                forward_payload: beginCell().endCell()
            }
        );

        // 4. 验证结果
        const totalRaisedUSDT = await onionAuction.getTotalRaisedUsdt();
        expect(totalRaisedUSDT).toBe(50000000n);
    });
});
```

## 注意事项

1. **Gas 费用** - USDT 操作需要更多的 Gas，因为涉及 Jetton 转账
2. **异步性** - USDT 购买是异步的，通过转账通知机制实现
3. **地址管理** - 需要正确管理 USDT 主合约和钱包地址
4. **测试环境** - 在测试网上使用测试 USDT 合约进行测试

## 相关文档

- [TON Jetton 标准 (TEP-74)](https://github.com/ton-blockchain/TEPs/blob/master/text/0074-jettons-standard.md)
- [TON Jetton 处理指南](https://docs.ton.org/v3/guidelines/dapps/asset-processing/jettons)
- [OnionAuction 合约设计文档](./CONTRACT_DESIGN.md)
