import { Address, beginCell, Cell, Contract, contractAddress, Contract<PERSON>rovider, Sender, SendMode } from '@ton/core';

export type UserPurchaseConfig = {};

export function userPurchaseConfigToCell(config: UserPurchaseConfig): Cell {
    return beginCell().endCell();
}

export class UserPurchase implements Contract {
    constructor(readonly address: Address, readonly init?: { code: Cell; data: Cell }) {}

    static createFromAddress(address: Address) {
        return new UserPurchase(address);
    }

    static createFromConfig(config: UserPurchaseConfig, code: Cell, workchain = 0) {
        const data = userPurchaseConfigToCell(config);
        const init = { code, data };
        return new UserPurchase(contractAddress(workchain, init), init);
    }

    async sendDeploy(provider: ContractProvider, via: Sender, value: bigint) {
        await provider.internal(via, {
            value,
            sendMode: SendMode.PAY_GAS_SEPARATELY,
            body: beginCell().endCell(),
        });
    }
}
