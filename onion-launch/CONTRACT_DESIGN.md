# ONION Token Fair Launch Contract Design

## Architecture Overview

The system consists of three main contracts:
1. **OnionAuction** - Main auction contract managing the fair launch process
2. **UserPurchase** - Child contract for each user's purchase records
3. **OnionJetton** - Token contract following TON jetton standard

## Message Flow Diagrams

### 1. Purchase Flow
```
User Wallet → OnionAuction: Purchase{amount, currency}
OnionAuction → UserPurchase: CreatePurchase{user, amount, tokens}
OnionAuction → OnionJetton: Mint{to: UserPurchase, amount}
UserPurchase → User Wallet: PurchaseConfirmation{tokens, price}
```

### 2. Refund Flow
```
User Wallet → UserPurchase: RequestRefund{purchase_id}
UserPurchase → OnionAuction: ProcessRefund{user, amount, fee}
OnionAuction → OnionJetton: Burn{from: UserPurchase, amount}
OnionAuction → User Wallet: RefundTransfer{amount - fee}
UserPurchase → User Wallet: RefundConfirmation{refunded_amount}
```

### 3. Auction Management Flow
```
Admin → OnionAuction: StartAuction{start_time, end_time, soft_cap, hard_cap}
OnionAuction → OnionAuction: UpdateRound{new_price, round_number}
OnionAuction → OnionAuction: CheckAuctionEnd{current_time, total_raised}
```

### 4. Token Distribution Flow (After Auction Success)
```
OnionAuction → OnionJetton: DistributeTokens{user_list}
OnionJetton → User Wallets: TransferTokens{amount per user}
```

## Contract Structure

### OnionAuction Contract State
```
- auction_config: AuctionConfig
- current_round: Int
- current_price: Int (in nanoTON)
- total_raised: Int
- total_tokens_sold: Int
- auction_status: Int (0=pending, 1=active, 2=ended)
- user_purchases: map<Address, Address> // user -> UserPurchase contract
```

### UserPurchase Contract State
```
- user_address: Address
- auction_address: Address
- total_purchased: Int
- total_paid: Int
- purchase_history: map<Int, Purchase>
- refund_history: map<Int, Refund>
```

### OnionJetton Contract State
```
- total_supply: Int
- auction_address: Address
- wallet_code: Cell
- user_wallets: map<Address, Address>
```

## Key Design Principles

1. **Bounded Data Structures**: All maps use user addresses as keys to avoid unbounded growth
2. **Child Contract Pattern**: Each user gets their own UserPurchase contract for purchase management
3. **Round-based Pricing**: English auction with time-based round progression
4. **Atomic Operations**: Each purchase/refund is handled atomically within gas limits
5. **Emergency Controls**: Admin functions for auction management and emergency stops

## Gas Optimization

- Use child contracts to distribute storage load
- Implement lazy deletion for refunded purchases  
- Batch operations where possible
- Use efficient data structures (Int vs String)

## Security Considerations

- Reentrancy protection on purchase/refund operations
- Access control for admin functions
- Overflow protection on arithmetic operations
- Time-based validation for auction phases
- Rate limiting for frequent operations