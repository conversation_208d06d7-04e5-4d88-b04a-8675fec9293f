import { Address, beginCell, Cell, Contract, contractAddress, ContractProvider, Sender, SendMode } from '@ton/core'

export type OnionAuctionConfig = {
    owner: Address
    startTime: number
    endTime: number
    softCap: bigint
    hardCap: bigint
    totalSupply: bigint
}

export function onionAuctionConfigToCell(config: OnionAuctionConfig): Cell {
    return beginCell()
        .storeAddress(config.owner)
        .storeUint(config.startTime, 64)
        .storeUint(config.endTime, 64)
        .storeCoins(config.softCap)
        .storeCoins(config.hardCap)
        .storeCoins(config.totalSupply)
        .endCell()
}

export class OnionAuction implements Contract {
    constructor(readonly address: Address, readonly init?: { code: Cell; data: Cell }) {}

    static createFromAddress(address: Address) {
        return new OnionAuction(address)
    }

    static createFromConfig(config: OnionAuctionConfig, code: Cell, workchain = 0) {
        const data = onionAuctionConfigToCell(config)
        const init = { code, data }
        return new OnionAuction(contractAddress(workchain, init), init)
    }

    async sendDeploy(provider: ContractProvider, via: Sender, value: bigint) {
        await provider.internal(via, {
            value,
            sendMode: SendMode.PAY_GAS_SEPARATELY,
            body: beginCell().endCell(),
        })
    }

    async sendPurchase(
        provider: ContractProvider,
        via: Sender,
        opts: {
            amount: bigint
            currency: number
            value: bigint
        }
    ) {
        await provider.internal(via, {
            value: opts.value,
            sendMode: SendMode.PAY_GAS_SEPARATELY,
            body: beginCell()
                .storeUint(0x1234, 32) // op code for Purchase
                .storeUint(0, 64) // query_id
                .storeCoins(opts.amount)
                .storeUint(opts.currency, 8)
                .endCell(),
        })
    }

    async sendRefund(
        provider: ContractProvider,
        via: Sender,
        opts: {
            purchaseId: number
            value: bigint
        }
    ) {
        await provider.internal(via, {
            value: opts.value,
            sendMode: SendMode.PAY_GAS_SEPARATELY,
            body: beginCell()
                .storeUint(0x5678, 32) // op code for Refund
                .storeUint(0, 64) // query_id
                .storeUint(opts.purchaseId, 32)
                .endCell(),
        })
    }

    async getAuctionInfo(provider: ContractProvider) {
        const result = await provider.get('auction_info', [])
        return {
            startTime: result.stack.readNumber(),
            endTime: result.stack.readNumber(),
            softCap: result.stack.readBigNumber(),
            hardCap: result.stack.readBigNumber(),
            totalSupply: result.stack.readBigNumber(),
            refundFeePercent: result.stack.readNumber(),
        }
    }

    async getCurrentRound(provider: ContractProvider) {
        const result = await provider.get('current_round', [])
        return result.stack.readNumber()
    }

    async getCurrentPrice(provider: ContractProvider) {
        const result = await provider.get('current_price', [])
        return result.stack.readBigNumber()
    }

    async getTotalRaised(provider: ContractProvider) {
        const result = await provider.get('total_raised', [])
        return result.stack.readBigNumber()
    }

    async getTotalTokensSold(provider: ContractProvider) {
        const result = await provider.get('total_tokens_sold', [])
        return result.stack.readBigNumber()
    }

    async getAuctionStatus(provider: ContractProvider) {
        const result = await provider.get('auction_status', [])
        return result.stack.readNumber()
    }

    async getRemainingTokens(provider: ContractProvider) {
        const result = await provider.get('remaining_tokens', [])
        return result.stack.readBigNumber()
    }

    async getUserPurchaseAddress(provider: ContractProvider, user: Address) {
        const result = await provider.get('user_purchase_address', [
            { type: 'slice', cell: beginCell().storeAddress(user).endCell() }
        ])
        return result.stack.readAddressOpt()
    }

    async isAuctionActive(provider: ContractProvider) {
        const result = await provider.get('is_auction_active', [])
        return result.stack.readBoolean()
    }
}