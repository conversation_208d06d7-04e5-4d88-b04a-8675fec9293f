'use client'

import { useState, useEffect } from 'react'

export interface AuctionData {
  totalRaised: number
  totalTokensSold: number
  auctionStatus: number
  currentRound: number
  currentPrice: number
  endTime: number
  softCap: number
  hardCap: number
  totalSupply: number
  startTime: number
}

export function useAuction() {
  const [auctionData, setAuctionData] = useState<AuctionData | null>(null)
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    // Simulate loading and fetch auction data
    const fetchAuctionData = async () => {
      setIsLoading(true)
      
      // Simulate API call delay
      await new Promise(resolve => setTimeout(resolve, 1000))
      
      // Mock data - in real app this would come from the smart contract
      const mockData: AuctionData = {
        totalRaised: 1600000, // $1.6M
        totalTokensSold: 673000, // 673k ONION
        auctionStatus: 1, // Active
        currentRound: 12,
        currentPrice: 0.11, // $0.11 per ONION
        endTime: Math.floor(Date.now() / 1000) + 86400, // 24 hours from now
        softCap: 500000, // $500k
        hardCap: 2000000, // $2M
        totalSupply: 1000000, // 1M ONION
        startTime: Math.floor(Date.now() / 1000) - 43200, // 12 hours ago
      }
      
      setAuctionData(mockData)
      setIsLoading(false)
    }

    fetchAuctionData()

    // Set up real-time updates every 2 minutes (as per requirements)
    const interval = setInterval(fetchAuctionData, 120000)

    return () => clearInterval(interval)
  }, [])

  return { auctionData, isLoading }
}