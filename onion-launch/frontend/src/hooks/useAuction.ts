'use client'

import { useState, useEffect } from 'react'
import { ApiService, formatCurrency, handleApiError } from '@/lib/apiService'

export interface AuctionData {
  totalRaised: number
  totalTokensSold: number
  auctionStatus: number
  currentRound: number
  currentPrice: number
  endTime: number
  softCap: number
  hardCap: number
  totalSupply: number
  startTime: number
  purchaseCount: number
}

export function useAuction() {
  const [auctionData, setAuctionData] = useState<AuctionData | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [isOnline, setIsOnline] = useState(true)

  useEffect(() => {
    // Fetch auction data from API service
    const fetchAuctionData = async () => {
      setIsLoading(true)
      setError(null)

      try {
        // Try to get real data from API service
        const state = await ApiService.getAuctionState()

        const auctionData: AuctionData = {
          totalRaised: parseFloat(formatCurrency(state.total_raised)),
          totalTokensSold: parseFloat(formatCurrency(state.total_tokens_sold)),
          auctionStatus: state.auction_status,
          currentRound: state.current_round,
          currentPrice: parseFloat(formatCurrency(state.current_price)),
          endTime: Math.floor(Date.now() / 1000) + 86400, // Mock end time
          softCap: 500000, // Mock soft cap
          hardCap: 2000000, // Mock hard cap
          totalSupply: 1000000, // Mock total supply
          startTime: Math.floor(Date.now() / 1000) - 43200, // Mock start time
          purchaseCount: state.purchase_count
        }

        setAuctionData(auctionData)
        setIsOnline(true)
      } catch (apiError) {
        console.warn('API service unavailable, using mock data:', apiError)
        setError(handleApiError(apiError))
        setIsOnline(false)

        // Fallback to mock data
        const mockData: AuctionData = {
          totalRaised: 1600000, // $1.6M
          totalTokensSold: 673000, // 673k ONION
          auctionStatus: 1, // Active
          currentRound: 12,
          currentPrice: 0.11, // $0.11 per ONION
          endTime: Math.floor(Date.now() / 1000) + 86400, // 24 hours from now
          softCap: 500000, // $500k
          hardCap: 2000000, // $2M
          totalSupply: 1000000, // 1M ONION
          startTime: Math.floor(Date.now() / 1000) - 43200, // 12 hours ago
          purchaseCount: 1250
        }

        setAuctionData(mockData)
      } finally {
        setIsLoading(false)
      }
    }

    fetchAuctionData()

    // Set up real-time updates every 2 minutes (as per requirements)
    const interval = setInterval(fetchAuctionData, 120000)

    return () => clearInterval(interval)
  }, [])

  return {
    auctionData,
    isLoading,
    error,
    isOnline,
    clearError: () => setError(null)
  }
}