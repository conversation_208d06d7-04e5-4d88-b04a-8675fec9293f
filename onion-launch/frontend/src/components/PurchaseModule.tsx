'use client'

import { useState } from 'react'
import { useTonWallet } from '@tonconnect/ui-react'
import { ShoppingCart, Calculator, AlertTriangle, CheckCircle } from 'lucide-react'

export function PurchaseModule() {
  const wallet = useTonWallet()
  const [amount, setAmount] = useState('')
  const [currency, setCurrency] = useState<'TON' | 'USDT'>('TON')
  const [isProcessing, setIsProcessing] = useState(false)

  const currentPrice = 0.11 // USD per ONION
  const tonPrice = 5.5 // USD per TON (mock price)
  const usdtPrice = 1.0 // USD per USDT

  const calculateTokens = () => {
    if (!amount) return 0
    const amountNum = parseFloat(amount)
    const amountInUSD = currency === 'TON' ? amountNum * tonPrice : amountNum * usdtPrice
    return amountInUSD / currentPrice
  }

  const isValidAmount = () => {
    if (!amount) return false
    const amountNum = parseFloat(amount)
    const minAmountUSD = 50
    const amountInUSD = currency === 'TON' ? amountNum * tonPrice : amountNum * usdtPrice
    return amountInUSD >= minAmountUSD
  }

  const handlePurchase = async () => {
    if (!wallet || !isValidAmount()) return

    setIsProcessing(true)
    try {
      // Here you would integrate with the actual TON smart contract
      console.log('Processing purchase:', { amount, currency, tokens: calculateTokens() })
      
      // Simulate processing delay
      await new Promise(resolve => setTimeout(resolve, 2000))
      
      // Reset form on success
      setAmount('')
      alert('Purchase successful!')
    } catch (error) {
      console.error('Purchase failed:', error)
      alert('Purchase failed. Please try again.')
    } finally {
      setIsProcessing(false)
    }
  }

  const tokensToReceive = calculateTokens()
  const minAmount = currency === 'TON' ? (50 / tonPrice).toFixed(2) : '50'

  return (
    <div className="bg-white rounded-2xl card-shadow p-6">
      <div className="flex items-center space-x-3 mb-6">
        <div className="flex items-center justify-center w-10 h-10 bg-onion-100 rounded-lg">
          <ShoppingCart className="w-5 h-5 text-onion-600" />
        </div>
        <h2 className="text-2xl font-bold text-gray-900">Purchase ONION</h2>
      </div>

      {!wallet ? (
        <div className="text-center py-8">
          <div className="flex items-center justify-center w-16 h-16 bg-gray-100 rounded-full mb-4 mx-auto">
            <AlertTriangle className="w-8 h-8 text-gray-400" />
          </div>
          <h3 className="text-lg font-medium text-gray-900 mb-2">Connect Your Wallet</h3>
          <p className="text-gray-600 mb-4">
            Connect your TON wallet to participate in the fair launch
          </p>
        </div>
      ) : (
        <div className="space-y-6">
          {/* Current Price Display */}
          <div className="bg-gradient-to-r from-onion-50 to-onion-100 rounded-lg p-4">
            <div className="flex justify-between items-center">
              <span className="text-sm font-medium text-onion-700">Current Price</span>
              <span className="text-lg font-bold text-onion-800">${currentPrice.toFixed(3)}</span>
            </div>
            <div className="text-xs text-onion-600 mt-1">per ONION token • Round 12</div>
          </div>

          {/* Currency Selection */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">Currency</label>
            <div className="grid grid-cols-2 gap-3">
              <button
                onClick={() => setCurrency('TON')}
                className={`p-3 rounded-lg border-2 transition-all ${
                  currency === 'TON'
                    ? 'border-blue-500 bg-blue-50 text-blue-700'
                    : 'border-gray-200 bg-white text-gray-700 hover:border-gray-300'
                }`}
              >
                <div className="font-medium">TON</div>
                <div className="text-xs text-gray-500">${tonPrice.toFixed(2)}</div>
              </button>
              <button
                onClick={() => setCurrency('USDT')}
                className={`p-3 rounded-lg border-2 transition-all ${
                  currency === 'USDT'
                    ? 'border-green-500 bg-green-50 text-green-700'
                    : 'border-gray-200 bg-white text-gray-700 hover:border-gray-300'
                }`}
              >
                <div className="font-medium">USDT</div>
                <div className="text-xs text-gray-500">${usdtPrice.toFixed(2)}</div>
              </button>
            </div>
          </div>

          {/* Amount Input */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Amount ({currency})
            </label>
            <div className="relative">
              <input
                type="number"
                value={amount}
                onChange={(e) => setAmount(e.target.value)}
                placeholder={`Min: ${minAmount} ${currency}`}
                className="w-full p-4 border border-gray-300 rounded-lg focus:ring-2 focus:ring-onion-500 focus:border-transparent"
                step="0.01"
              />
              <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
                <span className="text-sm font-medium text-gray-500">{currency}</span>
              </div>
            </div>
            <div className="mt-2 text-xs text-gray-500">
              Minimum: ${minAmount} {currency} (${50} USD equivalent)
            </div>
          </div>

          {/* Calculation Display */}
          {amount && (
            <div className="bg-gray-50 rounded-lg p-4">
              <div className="flex items-center space-x-2 mb-3">
                <Calculator className="w-4 h-4 text-gray-600" />
                <span className="text-sm font-medium text-gray-700">Calculation</span>
              </div>
              <div className="space-y-2 text-sm">
                <div className="flex justify-between">
                  <span className="text-gray-600">Amount in USD:</span>
                  <span className="font-medium">
                    ${((parseFloat(amount) || 0) * (currency === 'TON' ? tonPrice : usdtPrice)).toFixed(2)}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Price per ONION:</span>
                  <span className="font-medium">${currentPrice.toFixed(3)}</span>
                </div>
                <div className="border-t pt-2 flex justify-between">
                  <span className="text-gray-900 font-medium">You will receive:</span>
                  <span className="font-bold text-onion-600">
                    {tokensToReceive.toLocaleString(undefined, { maximumFractionDigits: 0 })} ONION
                  </span>
                </div>
              </div>
            </div>
          )}

          {/* Validation Messages */}
          {amount && !isValidAmount() && (
            <div className="flex items-center space-x-2 text-red-600 text-sm">
              <AlertTriangle className="w-4 h-4" />
              <span>Amount must be at least ${50} USD equivalent</span>
            </div>
          )}

          {amount && isValidAmount() && (
            <div className="flex items-center space-x-2 text-green-600 text-sm">
              <CheckCircle className="w-4 h-4" />
              <span>Valid purchase amount</span>
            </div>
          )}

          {/* Purchase Button */}
          <button
            onClick={handlePurchase}
            disabled={!isValidAmount() || isProcessing}
            className={`w-full py-4 rounded-lg font-semibold transition-all ${
              !isValidAmount() || isProcessing
                ? 'bg-gray-300 text-gray-500 cursor-not-allowed'
                : 'bg-gradient-to-r from-onion-500 to-onion-600 text-white hover:from-onion-600 hover:to-onion-700 transform hover:scale-[1.02]'
            }`}
          >
            {isProcessing ? (
              <div className="flex items-center justify-center space-x-2">
                <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                <span>Processing...</span>
              </div>
            ) : (
              'Purchase ONION Tokens'
            )}
          </button>

          {/* Risk Warning */}
          <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4 text-sm">
            <div className="flex items-center space-x-2 mb-2">
              <AlertTriangle className="w-4 h-4 text-yellow-600" />
              <span className="font-medium text-yellow-800">Important Notice</span>
            </div>
            <div className="text-yellow-700 space-y-1">
              <p>• Refunds available during auction with 5% fee</p>
              <p>• Tokens distributed after successful auction completion</p>
              <p>• Price may increase in future rounds</p>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}