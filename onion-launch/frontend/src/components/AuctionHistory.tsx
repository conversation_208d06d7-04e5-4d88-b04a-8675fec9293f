'use client'

import { History, TrendingUp, Clock } from 'lucide-react'

interface Round {
  id: number
  timeRange: string
  price: number
  volume: number
  endTime: string
}

export function AuctionHistory() {
  // Mock data - in real app this would come from the blockchain
  const rounds: Round[] = [
    { id: 12, timeRange: '14:00 - 15:00 UTC', price: 0.11, volume: 45230, endTime: 'Active' },
    { id: 11, timeRange: '13:00 - 14:00 UTC', price: 0.10, volume: 52100, endTime: '14:00' },
    { id: 10, timeRange: '12:00 - 13:00 UTC', price: 0.09, volume: 38750, endTime: '13:00' },
    { id: 9, timeRange: '11:00 - 12:00 UTC', price: 0.08, volume: 41200, endTime: '12:00' },
    { id: 8, timeRange: '10:00 - 11:00 UTC', price: 0.07, volume: 35600, endTime: '11:00' },
    { id: 7, timeRange: '09:00 - 10:00 UTC', price: 0.06, volume: 28900, endTime: '10:00' },
    { id: 6, timeRange: '08:00 - 09:00 UTC', price: 0.05, volume: 32400, endTime: '09:00' },
  ]

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 3,
      maximumFractionDigits: 3,
    }).format(amount)
  }

  const formatTokens = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount)
  }

  const getPriceChangeColor = (currentRound: Round, index: number) => {
    if (index === rounds.length - 1) return 'text-gray-500'
    const nextRound = rounds[index + 1]
    if (currentRound.price > nextRound.price) return 'text-green-600'
    if (currentRound.price < nextRound.price) return 'text-red-600'
    return 'text-gray-500'
  }

  const getPriceChangeIcon = (currentRound: Round, index: number) => {
    if (index === rounds.length - 1) return null
    const nextRound = rounds[index + 1]
    if (currentRound.price > nextRound.price) return '↗'
    if (currentRound.price < nextRound.price) return '↘'
    return '→'
  }

  return (
    <div className="bg-white rounded-2xl card-shadow p-6">
      <div className="flex items-center space-x-3 mb-6">
        <div className="flex items-center justify-center w-10 h-10 bg-purple-100 rounded-lg">
          <History className="w-5 h-5 text-purple-600" />
        </div>
        <h2 className="text-2xl font-bold text-gray-900">Auction History</h2>
      </div>

      <div className="overflow-x-auto">
        <table className="w-full">
          <thead>
            <tr className="border-b border-gray-200">
              <th className="text-left py-3 text-sm font-medium text-gray-600">Round</th>
              <th className="text-left py-3 text-sm font-medium text-gray-600">Time Range (UTC)</th>
              <th className="text-left py-3 text-sm font-medium text-gray-600">Price</th>
              <th className="text-left py-3 text-sm font-medium text-gray-600">Volume</th>
              <th className="text-left py-3 text-sm font-medium text-gray-600">Status</th>
            </tr>
          </thead>
          <tbody className="divide-y divide-gray-100">
            {rounds.map((round, index) => (
              <tr 
                key={round.id} 
                className={`hover:bg-gray-50 transition-colors ${
                  round.endTime === 'Active' ? 'bg-green-50' : ''
                }`}
              >
                <td className="py-4">
                  <div className="flex items-center space-x-2">
                    <span className="font-semibold text-gray-900">Round {round.id}</span>
                    {round.endTime === 'Active' && (
                      <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
                    )}
                  </div>
                </td>
                <td className="py-4">
                  <div className="flex items-center space-x-1 text-sm text-gray-600">
                    <Clock className="w-3 h-3" />
                    <span>{round.timeRange}</span>
                  </div>
                </td>
                <td className="py-4">
                  <div className="flex items-center space-x-2">
                    <span className={`font-semibold ${getPriceChangeColor(round, index)}`}>
                      {formatCurrency(round.price)}
                    </span>
                    {getPriceChangeIcon(round, index) && (
                      <span className={`text-xs ${getPriceChangeColor(round, index)}`}>
                        {getPriceChangeIcon(round, index)}
                      </span>
                    )}
                  </div>
                </td>
                <td className="py-4">
                  <div className="flex items-center space-x-1">
                    <span className="font-medium text-gray-900">
                      {formatTokens(round.volume)}
                    </span>
                    <span className="text-xs text-gray-500">ONION</span>
                  </div>
                </td>
                <td className="py-4">
                  {round.endTime === 'Active' ? (
                    <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                      <div className="w-1.5 h-1.5 bg-green-400 rounded-full mr-1.5 animate-pulse"></div>
                      Active
                    </span>
                  ) : (
                    <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                      Ended {round.endTime}
                    </span>
                  )}
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>

      {/* Summary Stats */}
      <div className="mt-6 grid grid-cols-3 gap-4 pt-6 border-t border-gray-200">
        <div className="text-center">
          <div className="text-2xl font-bold text-gray-900">12</div>
          <div className="text-sm text-gray-500">Total Rounds</div>
        </div>
        <div className="text-center">
          <div className="text-2xl font-bold text-green-600">+83%</div>
          <div className="text-sm text-gray-500">Price Growth</div>
        </div>
        <div className="text-center">
          <div className="text-2xl font-bold text-purple-600">
            {formatTokens(rounds.reduce((sum, round) => sum + round.volume, 0))}
          </div>
          <div className="text-sm text-gray-500">Total Volume</div>
        </div>
      </div>
    </div>
  )
}