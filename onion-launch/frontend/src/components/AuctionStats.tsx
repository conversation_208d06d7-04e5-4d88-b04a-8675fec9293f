'use client'

import { useState, useEffect } from 'react'
import { Clock, TrendingUp, Target, DollarSign } from 'lucide-react'
import { formatDistanceToNow } from 'date-fns'

interface AuctionStatsProps {
  data?: {
    totalRaised: number
    totalTokensSold: number
    auctionStatus: number
    currentRound: number
    currentPrice: number
    endTime: number
    softCap: number
    hardCap: number
    totalSupply: number
  }
}

export function AuctionStats({ data }: AuctionStatsProps) {
  const [timeLeft, setTimeLeft] = useState('')

  useEffect(() => {
    if (!data?.endTime) return

    const timer = setInterval(() => {
      const now = Date.now()
      const endTime = data.endTime * 1000
      
      if (now >= endTime) {
        setTimeLeft('Auction Ended')
        clearInterval(timer)
        return
      }

      setTimeLeft(formatDistanceToNow(endTime))
    }, 1000)

    return () => clearInterval(timer)
  }, [data?.endTime])

  const progress = data ? (data.totalRaised / data.hardCap) * 100 : 0
  const tokenProgress = data ? (data.totalTokensSold / data.totalSupply) * 100 : 0

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount)
  }

  const formatTokens = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount) + ' ONION'
  }

  return (
    <div className="bg-white rounded-2xl card-shadow p-6">
      <div className="flex items-center justify-between mb-6">
        <h2 className="text-2xl font-bold text-gray-900">Live Auction Stats</h2>
        <div className="flex items-center space-x-2">
          <div className="w-3 h-3 bg-green-500 rounded-full animate-pulse"></div>
          <span className="text-sm font-medium text-green-600">Active</span>
        </div>
      </div>

      {/* Main Stats Grid */}
      <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <div className="text-center">
          <div className="flex items-center justify-center w-12 h-12 bg-blue-100 rounded-xl mb-3 mx-auto">
            <DollarSign className="w-6 h-6 text-blue-600" />
          </div>
          <div className="text-2xl font-bold text-gray-900">
            {data ? formatCurrency(data.totalRaised) : formatCurrency(1600000)}
          </div>
          <div className="text-sm text-gray-500">Total Raised</div>
        </div>

        <div className="text-center">
          <div className="flex items-center justify-center w-12 h-12 bg-green-100 rounded-xl mb-3 mx-auto">
            <Target className="w-6 h-6 text-green-600" />
          </div>
          <div className="text-2xl font-bold text-gray-900">
            {data ? formatTokens(data.totalTokensSold) : formatTokens(673000)}
          </div>
          <div className="text-sm text-gray-500">Tokens Sold</div>
        </div>

        <div className="text-center">
          <div className="flex items-center justify-center w-12 h-12 bg-purple-100 rounded-xl mb-3 mx-auto">
            <TrendingUp className="w-6 h-6 text-purple-600" />
          </div>
          <div className="text-2xl font-bold text-gray-900">
            Round {data?.currentRound || 12}
          </div>
          <div className="text-sm text-gray-500">Current Round</div>
        </div>

        <div className="text-center">
          <div className="flex items-center justify-center w-12 h-12 bg-orange-100 rounded-xl mb-3 mx-auto">
            <Clock className="w-6 h-6 text-orange-600" />
          </div>
          <div className="text-lg font-bold text-gray-900">
            {timeLeft || '1d 12h'}
          </div>
          <div className="text-sm text-gray-500">Time Left</div>
        </div>
      </div>

      {/* Progress Bars */}
      <div className="space-y-6">
        {/* Funding Progress */}
        <div>
          <div className="flex justify-between items-center mb-2">
            <span className="text-sm font-medium text-gray-700">Funding Progress</span>
            <span className="text-sm font-bold text-gray-900">{progress.toFixed(1)}%</span>
          </div>
          <div className="w-full bg-gray-200 rounded-full h-3">
            <div 
              className="progress-bar h-3 rounded-full transition-all duration-500 ease-out"
              style={{ width: `${Math.min(progress, 100)}%` }}
            />
          </div>
          <div className="flex justify-between text-xs text-gray-500 mt-1">
            <span>Soft Cap: {data ? formatCurrency(data.softCap) : formatCurrency(500000)}</span>
            <span>Hard Cap: {data ? formatCurrency(data.hardCap) : formatCurrency(2000000)}</span>
          </div>
        </div>

        {/* Token Distribution Progress */}
        <div>
          <div className="flex justify-between items-center mb-2">
            <span className="text-sm font-medium text-gray-700">Token Distribution</span>
            <span className="text-sm font-bold text-gray-900">{tokenProgress.toFixed(1)}%</span>
          </div>
          <div className="w-full bg-gray-200 rounded-full h-3">
            <div 
              className="bg-gradient-to-r from-onion-500 to-onion-600 h-3 rounded-full transition-all duration-500 ease-out"
              style={{ width: `${Math.min(tokenProgress, 100)}%` }}
            />
          </div>
          <div className="flex justify-between text-xs text-gray-500 mt-1">
            <span>0 ONION</span>
            <span>{data ? formatTokens(data.totalSupply) : formatTokens(1000000)}</span>
          </div>
        </div>

        {/* Remaining Tokens Alert */}
        {tokenProgress > 80 && (
          <div className="bg-orange-50 border border-orange-200 rounded-lg p-4">
            <div className="flex items-center space-x-2">
              <Target className="w-5 h-5 text-orange-600" />
              <span className="text-sm font-medium text-orange-800">
                Only {data ? formatTokens(data.totalSupply - data.totalTokensSold) : formatTokens(327000)} tokens remaining!
              </span>
            </div>
          </div>
        )}
      </div>
    </div>
  )
}