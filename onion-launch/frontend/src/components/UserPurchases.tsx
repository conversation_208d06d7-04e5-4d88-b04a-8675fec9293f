'use client'

import { useState } from 'react'
import { useTonWallet } from '@tonconnect/ui-react'
import { User, RefreshCw, AlertTriangle, CheckCircle, X } from 'lucide-react'

interface Purchase {
  id: number
  amount: number
  currency: 'TON' | 'USDT'
  tokens: number
  price: number
  timestamp: string
  status: 'completed' | 'pending' | 'refunded'
  canRefund: boolean
}

export function UserPurchases() {
  const wallet = useTonWallet()
  const [isRefunding, setIsRefunding] = useState<number | null>(null)

  // Mock user purchases - in real app this would come from the smart contract
  const [purchases, setPurchases] = useState<Purchase[]>([
    {
      id: 1,
      amount: 100,
      currency: 'TON',
      tokens: 5000,
      price: 0.11,
      timestamp: '2025-01-12 14:30 UTC',
      status: 'completed',
      canRefund: true
    },
    {
      id: 2,
      amount: 200,
      currency: 'USDT',
      tokens: 1818,
      price: 0.11,
      timestamp: '2025-01-12 15:15 UTC',
      status: 'pending',
      canRefund: true
    }
  ])

  const handleRefund = async (purchaseId: number) => {
    if (!wallet) return

    setIsRefunding(purchaseId)
    try {
      // Here you would integrate with the smart contract's refund function
      console.log('Processing refund for purchase:', purchaseId)
      
      // Simulate processing delay
      await new Promise(resolve => setTimeout(resolve, 2000))
      
      // Update purchase status
      setPurchases(prev => prev.map(p => 
        p.id === purchaseId 
          ? { ...p, status: 'refunded' as const, canRefund: false }
          : p
      ))
      
      alert('Refund processed successfully!')
    } catch (error) {
      console.error('Refund failed:', error)
      alert('Refund failed. Please try again.')
    } finally {
      setIsRefunding(null)
    }
  }

  const formatCurrency = (amount: number, currency: string) => {
    return `${amount.toLocaleString()} ${currency}`
  }

  const formatTokens = (amount: number) => {
    return `${amount.toLocaleString()} ONION`
  }

  const getTotalPurchased = () => {
    return purchases
      .filter(p => p.status !== 'refunded')
      .reduce((sum, p) => sum + p.tokens, 0)
  }

  const getTotalInvested = () => {
    return purchases
      .filter(p => p.status !== 'refunded')
      .reduce((sum, p) => {
        const usdValue = p.currency === 'TON' ? p.amount * 5.5 : p.amount
        return sum + usdValue
      }, 0)
  }

  if (!wallet) {
    return (
      <div className="bg-white rounded-2xl card-shadow p-6">
        <div className="flex items-center space-x-3 mb-6">
          <div className="flex items-center justify-center w-10 h-10 bg-gray-100 rounded-lg">
            <User className="w-5 h-5 text-gray-400" />
          </div>
          <h2 className="text-2xl font-bold text-gray-900">Your Purchases</h2>
        </div>
        
        <div className="text-center py-8">
          <div className="flex items-center justify-center w-16 h-16 bg-gray-100 rounded-full mb-4 mx-auto">
            <User className="w-8 h-8 text-gray-400" />
          </div>
          <p className="text-gray-600">Connect wallet to view your purchases</p>
        </div>
      </div>
    )
  }

  return (
    <div className="bg-white rounded-2xl card-shadow p-6">
      <div className="flex items-center space-x-3 mb-6">
        <div className="flex items-center justify-center w-10 h-10 bg-blue-100 rounded-lg">
          <User className="w-5 h-5 text-blue-600" />
        </div>
        <h2 className="text-2xl font-bold text-gray-900">Your Purchases</h2>
      </div>

      {/* Summary Stats */}
      <div className="grid grid-cols-2 gap-4 mb-6">
        <div className="bg-gradient-to-r from-blue-50 to-blue-100 rounded-lg p-4">
          <div className="text-2xl font-bold text-blue-700">
            {formatTokens(getTotalPurchased())}
          </div>
          <div className="text-sm text-blue-600">Total Purchased</div>
        </div>
        <div className="bg-gradient-to-r from-green-50 to-green-100 rounded-lg p-4">
          <div className="text-2xl font-bold text-green-700">
            ${getTotalInvested().toLocaleString()}
          </div>
          <div className="text-sm text-green-600">Total Invested</div>
        </div>
      </div>

      {/* Purchase History */}
      <div className="space-y-4">
        <h3 className="text-lg font-semibold text-gray-900">Purchase History</h3>
        
        {purchases.length === 0 ? (
          <div className="text-center py-8">
            <div className="flex items-center justify-center w-16 h-16 bg-gray-100 rounded-full mb-4 mx-auto">
              <AlertTriangle className="w-8 h-8 text-gray-400" />
            </div>
            <p className="text-gray-600">No purchases yet</p>
          </div>
        ) : (
          <div className="space-y-3">
            {purchases.map((purchase) => (
              <div 
                key={purchase.id}
                className="border border-gray-200 rounded-lg p-4 hover:border-gray-300 transition-colors"
              >
                <div className="flex justify-between items-start">
                  <div className="flex-1">
                    <div className="flex items-center space-x-2 mb-2">
                      <span className="text-sm font-medium text-gray-900">
                        Purchase #{purchase.id}
                      </span>
                      {purchase.status === 'completed' && (
                        <CheckCircle className="w-4 h-4 text-green-500" />
                      )}
                      {purchase.status === 'pending' && (
                        <div className="w-4 h-4 border-2 border-blue-500 border-t-transparent rounded-full animate-spin" />
                      )}
                      {purchase.status === 'refunded' && (
                        <X className="w-4 h-4 text-red-500" />
                      )}
                    </div>
                    
                    <div className="grid grid-cols-2 gap-4 text-sm">
                      <div>
                        <span className="text-gray-600">Amount:</span>
                        <span className="ml-2 font-medium">
                          {formatCurrency(purchase.amount, purchase.currency)}
                        </span>
                      </div>
                      <div>
                        <span className="text-gray-600">Tokens:</span>
                        <span className="ml-2 font-medium">
                          {formatTokens(purchase.tokens)}
                        </span>
                      </div>
                      <div>
                        <span className="text-gray-600">Price:</span>
                        <span className="ml-2 font-medium">${purchase.price.toFixed(3)}</span>
                      </div>
                      <div>
                        <span className="text-gray-600">Time:</span>
                        <span className="ml-2 font-medium">{purchase.timestamp}</span>
                      </div>
                    </div>

                    <div className="mt-2 flex items-center space-x-2">
                      {purchase.status === 'completed' && (
                        <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                          Completed
                        </span>
                      )}
                      {purchase.status === 'pending' && (
                        <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                          Pending
                        </span>
                      )}
                      {purchase.status === 'refunded' && (
                        <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                          Refunded
                        </span>
                      )}
                    </div>
                  </div>

                  {/* Refund Button */}
                  {purchase.canRefund && purchase.status !== 'refunded' && (
                    <button
                      onClick={() => handleRefund(purchase.id)}
                      disabled={isRefunding === purchase.id}
                      className="ml-4 px-3 py-1.5 text-sm font-medium text-red-600 border border-red-200 rounded-lg hover:bg-red-50 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                      {isRefunding === purchase.id ? (
                        <div className="flex items-center space-x-1">
                          <RefreshCw className="w-3 h-3 animate-spin" />
                          <span>Refunding...</span>
                        </div>
                      ) : (
                        'Refund'
                      )}
                    </button>
                  )}
                </div>
              </div>
            ))}
          </div>
        )}
      </div>

      {/* Refund Info */}
      {purchases.some(p => p.canRefund && p.status !== 'refunded') && (
        <div className="mt-6 p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
          <div className="flex items-center space-x-2 mb-2">
            <AlertTriangle className="w-4 h-4 text-yellow-600" />
            <span className="text-sm font-medium text-yellow-800">Refund Policy</span>
          </div>
          <div className="text-sm text-yellow-700">
            <p>• Refunds available during auction period only</p>
            <p>• 5% fee will be deducted from refund amount</p>
            <p>• Refunds processed immediately upon confirmation</p>
          </div>
        </div>
      )}
    </div>
  )
}