'use client'

import { TonConnectButton, useTonWallet } from '@tonconnect/ui-react'
import { Users, Clock, TrendingUp } from 'lucide-react'

export function Header() {
  const wallet = useTonWallet()

  return (
    <header className="bg-white shadow-sm border-b border-gray-200">
      <div className="container mx-auto px-4 py-4">
        <div className="flex justify-between items-center">
          <div className="flex items-center space-x-4">
            <div className="flex items-center space-x-2">
              <span className="text-2xl">🧅</span>
              <span className="text-xl font-bold text-gray-900">ONION</span>
            </div>
            
            {/* Live indicators */}
            <div className="hidden md:flex items-center space-x-6 ml-8">
              <div className="flex items-center space-x-2 text-sm text-gray-600">
                <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
                <span>Live Auction</span>
              </div>
              <div className="flex items-center space-x-1 text-sm text-gray-600">
                <Users className="w-4 h-4" />
                <span>247 Participants</span>
              </div>
              <div className="flex items-center space-x-1 text-sm text-gray-600">
                <TrendingUp className="w-4 h-4" />
                <span>Round 12</span>
              </div>
            </div>
          </div>

          <div className="flex items-center space-x-4">
            {wallet && (
              <div className="hidden md:block text-sm text-gray-600">
                <span className="font-medium">Connected:</span>
                <span className="ml-2 font-mono">
                  {wallet.account.address.slice(0, 6)}...{wallet.account.address.slice(-4)}
                </span>
              </div>
            )}
            <TonConnectButton />
          </div>
        </div>
      </div>
    </header>
  )
}