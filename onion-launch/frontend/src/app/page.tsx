'use client'

import { useState, useEffect } from 'react'
import { AuctionInfo } from '@/components/AuctionInfo'
import { AuctionStats } from '@/components/AuctionStats'
import { PurchaseModule } from '@/components/PurchaseModule'
import { AuctionHistory } from '@/components/AuctionHistory'
import { UserPurchases } from '@/components/UserPurchases'
import { useAuction } from '@/hooks/useAuction'

export default function Home() {
  const { auctionData, isLoading } = useAuction()

  if (isLoading) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="flex items-center justify-center min-h-[60vh]">
          <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-onion-600"></div>
        </div>
      </div>
    )
  }

  return (
    <div className="container mx-auto px-4 py-8">
      {/* Hero Section */}
      <div className="text-center mb-12">
        <div className="inline-flex items-center justify-center w-20 h-20 bg-gradient-to-r from-onion-500 to-onion-600 rounded-full mb-6">
          <span className="text-3xl font-bold text-white">🧅</span>
        </div>
        <h1 className="text-4xl lg:text-6xl font-bold bg-gradient-to-r from-onion-600 to-onion-700 bg-clip-text text-transparent mb-4">
          ONION Token Fair Launch
        </h1>
        <p className="text-xl text-gray-600 max-w-3xl mx-auto">
          Experience the future of fair token distribution with our English auction mechanism. 
          Equal opportunity for all participants with transparent pricing.
        </p>
      </div>

      {/* Main Grid */}
      <div className="grid lg:grid-cols-3 gap-8">
        {/* Left Column - Auction Info & Stats */}
        <div className="lg:col-span-2 space-y-8">
          <AuctionStats data={auctionData} />
          <AuctionInfo data={auctionData} />
          <AuctionHistory />
        </div>

        {/* Right Column - Purchase & User Info */}
        <div className="space-y-8">
          <PurchaseModule />
          <UserPurchases />
        </div>
      </div>

      {/* Footer */}
      <footer className="mt-16 pt-8 border-t border-gray-200 text-center text-gray-500">
        <p>&copy; 2025 ONION Token. All rights reserved.</p>
        <p className="mt-2 text-sm">
          Built on TON Blockchain with Tact smart contracts
        </p>
      </footer>
    </div>
  )
}