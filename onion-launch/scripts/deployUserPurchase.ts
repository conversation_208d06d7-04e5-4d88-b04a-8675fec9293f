import { to<PERSON><PERSON> } from '@ton/core';
import { UserPurchase } from '../wrappers/UserPurchase';
import { compile, NetworkProvider } from '@ton/blueprint';

export async function run(provider: NetworkProvider) {
    const userPurchase = provider.open(UserPurchase.createFromConfig({}, await compile('UserPurchase')));

    await userPurchase.sendDeploy(provider.sender(), toNano('0.05'));

    await provider.waitForDeploy(userPurchase.address);

    // run methods on `userPurchase`
}
