import { Blockchain, SandboxContract, TreasuryContract } from '@ton/sandbox';
import { Cell, toNano } from '@ton/core';
import { UserPurchase } from '../wrappers/UserPurchase';
import '@ton/test-utils';
import { compile } from '@ton/blueprint';

describe('UserPurchase', () => {
    let code: Cell;

    beforeAll(async () => {
        code = await compile('UserPurchase');
    });

    let blockchain: Blockchain;
    let deployer: SandboxContract<TreasuryContract>;
    let userPurchase: SandboxContract<UserPurchase>;

    beforeEach(async () => {
        blockchain = await Blockchain.create();

        userPurchase = blockchain.openContract(UserPurchase.createFromConfig({}, code));

        deployer = await blockchain.treasury('deployer');

        const deployResult = await userPurchase.sendDeploy(deployer.getSender(), toNano('0.05'));

        expect(deployResult.transactions).toHaveTransaction({
            from: deployer.address,
            to: userPurchase.address,
            deploy: true,
            success: true,
        });
    });

    it('should deploy', async () => {
        // the check is done inside beforeEach
        // blockchain and userPurchase are ready to use
    });
});
