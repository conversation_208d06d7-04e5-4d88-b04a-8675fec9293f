{"name": "onion-launch", "version": "0.0.1", "scripts": {"bp": "blueprint", "start": "blueprint run", "build": "blueprint build", "test": "jest --verbose", "release": "blueprint pack && npm publish --access public"}, "dependencies": {"@ton/core": "~0"}, "devDependencies": {"@nowarp/misti": "^0.9.0", "@tact-lang/compiler": ">=1.6.13 <2.0.0", "@ton-community/func-js": ">=0.9.1", "@ton/blueprint": ">=0.38.0", "@ton/crypto": "^3.3.0", "@ton/sandbox": ">=0.35.0", "@ton/test-utils": ">=0.9.0", "@ton/tolk-js": ">=1.0.0", "@ton/ton": ">=15.2.1 <16.0.0", "@types/jest": "^29.5.14", "@types/node": "^22.15.32", "jest": "^29.7.0", "prettier": "^3.5.3", "ts-jest": "^29.4.0", "ts-node": "^10.9.2", "typescript": "^5.8.3"}}