/**
 * OnionAuction USDT Purchase Example
 * 
 * This example demonstrates how to interact with the OnionAuction contract
 * that supports both TON and USDT purchases.
 */

import { Address, toNano, beginCell } from '@ton/core';
import { TonClient } from '@ton/ton';
import { OnionAuction } from '../build/OnionAuction/OnionAuction_OnionAuction';

// Configuration
const AUCTION_ADDRESS = 'EQxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx'; // Replace with actual auction address
const USDT_MASTER_ADDRESS = 'EQBynBO23ywHy_CgarY9NK9FTz0yDsG82PtcbSTQgGoXwiuA'; // Testnet USDT
const USER_ADDRESS = 'EQxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx'; // Replace with user address

async function main() {
    // Initialize TON client
    const client = new TonClient({
        endpoint: 'https://testnet.toncenter.com/api/v2/jsonRPC',
        apiKey: 'your-api-key' // Get from @tonapibot
    });

    // Open auction contract
    const auction = client.open(
        OnionAuction.fromAddress(Address.parse(AUCTION_ADDRESS))
    );

    console.log('🔍 Checking auction status...');

    // Check if auction is active
    const isActive = await auction.getIsAuctionActive();
    console.log('Auction active:', isActive);

    // Check if USDT is enabled
    const isUSDTEnabled = await auction.getIsUsdtEnabled();
    console.log('USDT enabled:', isUSDTEnabled);

    if (isUSDTEnabled) {
        const usdtConfig = await auction.getUsdtConfig();
        console.log('USDT Master:', usdtConfig?.master_address.toString());
        console.log('USDT Wallet:', usdtConfig?.wallet_address?.toString());
    }

    // Get current auction info
    const auctionInfo = await auction.getAuctionInfo();
    console.log('\n📊 Auction Information:');
    console.log('Soft Cap:', (Number(auctionInfo.soft_cap) / 1e9).toLocaleString(), 'TON');
    console.log('Hard Cap:', (Number(auctionInfo.hard_cap) / 1e9).toLocaleString(), 'TON');
    console.log('Total Supply:', (Number(auctionInfo.total_supply) / 1e9).toLocaleString(), 'tokens');

    // Get current price and raised amounts
    const currentPrice = await auction.getCurrentPrice();
    const totalRaisedTON = await auction.getTotalRaised();
    const totalRaisedUSDT = await auction.getTotalRaisedUsdt();
    const totalRaisedEquivalent = await auction.getTotalRaisedEquivalent();

    console.log('\n💰 Current Status:');
    console.log('Current Price:', (Number(currentPrice) / 1e9).toFixed(4), 'TON per token');
    console.log('Total Raised (TON):', (Number(totalRaisedTON) / 1e9).toLocaleString(), 'TON');
    console.log('Total Raised (USDT):', (Number(totalRaisedUSDT) / 1e6).toLocaleString(), 'USDT');
    console.log('Total Raised (Equivalent):', (Number(totalRaisedEquivalent) / 1e9).toLocaleString(), 'TON');

    console.log('\n📝 Example Purchase Transactions:');

    // Example 1: TON Purchase
    console.log('\n1️⃣ TON Purchase Example:');
    console.log('```typescript');
    console.log('// Purchase 100 tokens with TON');
    console.log('const tonPurchase = {');
    console.log('    $$type: "Purchase",');
    console.log('    amount: toNano("50"), // 50 TON');
    console.log('    currency: 0n // TON currency');
    console.log('};');
    console.log('');
    console.log('await auction.send(');
    console.log('    sender,');
    console.log('    { value: toNano("52") }, // 50 TON + 2 TON for gas');
    console.log('    tonPurchase');
    console.log(');');
    console.log('```');

    // Example 2: USDT Purchase
    console.log('\n2️⃣ USDT Purchase Example:');
    console.log('```typescript');
    console.log('// Step 1: Get user\'s USDT wallet');
    console.log('const usdtMaster = client.open(');
    console.log('    JettonMaster.fromAddress(Address.parse("' + USDT_MASTER_ADDRESS + '"))');
    console.log(');');
    console.log('const userUSDTWallet = await usdtMaster.getWalletAddress(userAddress);');
    console.log('');
    console.log('// Step 2: Send USDT to auction contract');
    console.log('const usdtWallet = client.open(');
    console.log('    JettonWallet.fromAddress(userUSDTWallet)');
    console.log(');');
    console.log('');
    console.log('const usdtTransfer = {');
    console.log('    $$type: "JettonTransfer",');
    console.log('    query_id: 0n,');
    console.log('    amount: 50000000n, // 50 USDT (6 decimals)');
    console.log('    destination: Address.parse("' + AUCTION_ADDRESS + '"),');
    console.log('    response_destination: userAddress,');
    console.log('    custom_payload: null,');
    console.log('    forward_ton_amount: 1n, // 1 nanoton for notification');
    console.log('    forward_payload: beginCell().endCell()');
    console.log('};');
    console.log('');
    console.log('await usdtWallet.send(');
    console.log('    sender,');
    console.log('    { value: toNano("0.1") }, // Gas for jetton transfer');
    console.log('    usdtTransfer');
    console.log(');');
    console.log('```');

    // Example 3: Check user purchase
    console.log('\n3️⃣ Check User Purchase:');
    console.log('```typescript');
    console.log('// Get user purchase contract address (always returns an address)');
    console.log('const userPurchaseAddress = await auction.getUserPurchaseAddress(userAddress);');
    console.log('');
    console.log('// Open user purchase contract');
    console.log('const userPurchase = client.open(');
    console.log('    UserPurchase.fromAddress(userPurchaseAddress)');
    console.log(');');
    console.log('');
    console.log('// Check if contract exists and get purchase data');
    console.log('try {');
    console.log('    const totalPurchased = await userPurchase.getTotalPurchased();');
    console.log('    const totalPaid = await userPurchase.getTotalPaid();');
    console.log('    ');
    console.log('    console.log("Total tokens purchased:", totalPurchased);');
    console.log('    console.log("Total amount paid:", totalPaid);');
    console.log('} catch (error) {');
    console.log('    console.log("User has not made any purchases yet");');
    console.log('}');
    console.log('```');

    // Example 4: Refund
    console.log('\n4️⃣ Request Refund:');
    console.log('```typescript');
    console.log('// Request refund for a specific purchase');
    console.log('const refundRequest = {');
    console.log('    $$type: "Refund",');
    console.log('    purchase_id: 1n // ID of the purchase to refund');
    console.log('};');
    console.log('');
    console.log('await userPurchase.send(');
    console.log('    sender,');
    console.log('    { value: toNano("0.1") },');
    console.log('    refundRequest');
    console.log(');');
    console.log('```');

    console.log('\n✅ Example completed!');
    console.log('\n📚 Additional Resources:');
    console.log('- USDT Integration Guide: ./USDT_INTEGRATION.md');
    console.log('- Contract Design: ./CONTRACT_DESIGN.md');
    console.log('- TON Jetton Docs: https://docs.ton.org/v3/guidelines/dapps/asset-processing/jettons');
}

// Helper function to calculate token amount from payment
function calculateTokenAmount(paymentAmount: bigint, currentPrice: bigint): bigint {
    // For TON: tokens = (payment * 1e9) / price
    // For USDT: tokens = (payment * 1000 * 1e9) / price (convert 6 decimals to 9)
    return (paymentAmount * 1000000000n) / currentPrice;
}

// Helper function to format amounts
function formatAmount(amount: bigint, decimals: number): string {
    const divisor = BigInt(10 ** decimals);
    const whole = amount / divisor;
    const fraction = amount % divisor;
    return `${whole}.${fraction.toString().padStart(decimals, '0')}`;
}

// Run the example
if (require.main === module) {
    main().catch(console.error);
}

export { main, calculateTokenAmount, formatAmount };
