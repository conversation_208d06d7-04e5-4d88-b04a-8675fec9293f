# ONION Token Fair Launch Implementation

## 🎯 Overview

Complete implementation of a TON Blockchain-based token fair launch platform featuring:

- **Smart Contracts**: Tact-based implementation with main auction, user purchase child contracts, and jetton token
- **Multi-Currency Support**: Both TON and USDT (Jetton) payment options
- **Frontend**: Next.js-based demo interface with real-time auction tracking
- **Security**: Misti static analysis integration for contract security validation

## 📁 Project Structure

```
onion-launch/
├── contracts/                 # Smart Contracts
│   ├── onion_auction.tact    # Main auction contract with child contracts
│   └── onion_jetton.tact     # Jetton token implementation
├── frontend/                 # Next.js Demo Application
│   ├── src/app/             # App router pages
│   ├── src/components/      # React components
│   ├── src/hooks/           # Custom hooks
│   └── src/lib/             # Utilities and contract interfaces
├── tests/                   # Contract tests
├── scripts/                 # Deployment scripts
└── CONTRACT_DESIGN.md       # Technical design documentation
```

## 🏗️ Architecture Design

### Smart Contract Architecture

1. **OnionAuction** (Main Contract)
   - Manages auction lifecycle and pricing
   - Creates UserPurchase child contracts for each user
   - Implements English auction mechanism with round-based pricing
   - Supports both TON and USDT payments via Jetton standard
   - Handles refunds with 5% fee structure

2. **UserPurchase** (Child Contracts)
   - Individual purchase tracking per user
   - Refund request processing
   - Purchase history storage

3. **OnionJetton** (Token Contract)
   - Standard jetton implementation
   - Minting controlled by auction contract
   - Wallet management for token holders

### Key Features

- ✅ **English Auction**: Time-based rounds with automatic price increments
- ✅ **Multi-Currency**: Support for both TON and USDT payments
- ✅ **Child Contract Pattern**: Unbounded data prevention using per-user contracts
- ✅ **Refund System**: 5% fee refunds during auction period (supports both currencies)
- ✅ **Security**: Misti static analysis validation
- ✅ **Real-time UI**: Live auction tracking with 2-minute updates

## 🚀 Getting Started

### Prerequisites

- Bun runtime
- Node.js 18+
- TON development environment

### Installation & Setup

1. **Install Dependencies**
   ```bash
   cd onion-launch
   bun install
   ```

2. **Build Contracts**
   ```bash
   npx blueprint build
   ```

3. **Run Tests**
   ```bash
   npx blueprint test
   ```

4. **Static Analysis**
   ```bash
   npx misti contracts/onion_auction.tact
   ```

5. **Start Frontend**
   ```bash
   cd frontend
   bun install
   bun run dev
   ```

## 🎮 Frontend Demo

The Next.js frontend provides a complete auction interface:

- **Live Auction Stats**: Real-time progress tracking
- **Purchase Module**: TON/USDT support with live calculations  
- **Auction History**: Round-by-round price progression
- **User Dashboard**: Purchase history and refund functionality
- **TON Connect**: Wallet integration for seamless interaction

### Key UI Components

- `AuctionStats`: Live progress bars and metrics
- `PurchaseModule`: Token purchase interface
- `AuctionHistory`: Historical round data
- `UserPurchases`: Personal transaction management

## 🔐 Security Analysis

Misti static analysis results show:
- **Medium**: 4 issues (SuboptimalSend recommendations)
- **Low**: 17 issues (PreferredStdlibApi optimizations)
- **Clean**: No critical security vulnerabilities

## 📊 Auction Mechanics

### English Auction System
- **Round Duration**: 1 hour per round
- **Price Increment**: 0.01 TON per round
- **Minimum Purchase**: 50 TON equivalent
- **Soft Cap**: $500K USD
- **Hard Cap**: $2M USD
- **Total Supply**: 1M ONION tokens

### Purchase Flow
1. Connect TON wallet
2. Select currency (TON/USDT)
3. Enter purchase amount
4. Confirm transaction
5. Receive tokens in user's wallet

### Refund System
- Available during auction period only
- 5% fee deducted from refund amount
- Immediate processing upon confirmation
- Updates remaining token supply

## 🛠️ Technical Implementation

### Smart Contract Features
- **Gas Optimization**: Child contracts distribute storage load
- **Access Control**: Owner-only admin functions
- **Time Validation**: Auction phase enforcement
- **Overflow Protection**: Safe arithmetic operations
- **Reentrancy Guards**: Secure state transitions

### Frontend Integration
- **TON Connect**: Seamless wallet connectivity
- **Real-time Updates**: Live auction data synchronization
- **Responsive Design**: Mobile-first UI approach
- **TypeScript**: Full type safety throughout

## 💰 USDT Integration

The platform now supports USDT payments through TON's Jetton standard:

### Features
- **Dual Currency Support**: Accept both TON and USDT payments
- **Automatic Conversion**: USDT amounts converted to TON equivalent for pricing
- **Jetton Standard Compliance**: Full TEP-74 implementation
- **Secure Validation**: Verify USDT transfers from correct Jetton wallets
- **Unified Refunds**: Support refunds in both currencies

### Setup
1. Deploy auction contract
2. Configure USDT master contract address
3. Set auction contract's USDT wallet address
4. Enable USDT purchases

See `USDT_INTEGRATION.md` for detailed implementation guide.

## 📋 Testing & Validation

The implementation includes comprehensive testing:
- Contract deployment validation
- Auction lifecycle testing
- Purchase and refund flow verification (TON and USDT)
- Multi-currency integration testing
- Error handling and edge cases
- Static security analysis

## 🚀 Deployment

Ready for mainnet deployment with:
- Production-optimized contracts
- Security-validated implementation
- Complete frontend integration
- Comprehensive testing suite

---

**🧅 ONION Token Fair Launch** - Revolutionizing token distribution on TON Blockchain
