# 签名验证设计方案

## 概述

将 token 计算过程从链上移到链下，使用 `checkSignature` 方法在链上验证计算结果的正确性。这种设计提高了灵活性，降低了 gas 成本，并允许更复杂的计算逻辑。

## 当前链上计算逻辑分析

### 现有计算流程
1. **价格计算**: 基于时间的轮次更新
   ```tact
   let elapsed_time: Int = current_time - self.auction_config.start_time;
   let new_round: Int = (elapsed_time / self.ROUND_DURATION) + 1;
   self.current_price += self.PRICE_INCREMENT * (new_round - self.current_round);
   ```

2. **Token 计算**: 
   ```tact
   let tokens_to_receive: Int = (msg.amount * ton("1")) / self.current_price;
   ```

3. **验证逻辑**:
   - 最小购买金额检查
   - 剩余 token 供应量检查
   - 拍卖状态和时间检查

## 新的签名验证方案

### 1. 数据结构设计

#### 购买计算数据结构
```tact
struct PurchaseCalculation {
    user: Address;
    amount: Int as coins;
    currency: Int as uint8; // 0=TON, 1=USDT
    tokens_to_receive: Int as coins;
    current_price: Int as coins;
    current_round: Int as uint32;
    timestamp: Int as uint64;
    nonce: Int as uint64; // 防重放攻击
}
```

#### 签名验证消息
```tact
message PurchaseWithSignature {
    calculation: PurchaseCalculation;
    signature: Slice; // 服务器签名
}
```

### 2. 签名生成流程

#### 链下服务器端
1. **接收购买请求**: 用户发送购买金额和货币类型
2. **计算当前价格**: 基于当前时间和拍卖参数
3. **计算 token 数量**: 使用相同的计算逻辑
4. **生成签名数据**: 
   ```typescript
   const dataToSign = {
     user: userAddress,
     amount: purchaseAmount,
     currency: currencyType,
     tokens_to_receive: calculatedTokens,
     current_price: currentPrice,
     current_round: currentRound,
     timestamp: currentTimestamp,
     nonce: uniqueNonce
   }
   ```
5. **生成签名**: 使用服务器私钥对数据进行签名
6. **返回结果**: 返回计算数据和签名

### 3. 链上验证流程

#### 合约验证步骤
1. **签名验证**: 使用 `checkSignature` 验证服务器签名
2. **时间窗口验证**: 确保计算时间在合理范围内
3. **重放攻击防护**: 检查 nonce 是否已使用
4. **计算结果验证**: 可选的简单验证逻辑
5. **业务逻辑验证**: 最小金额、供应量等检查

### 4. 安全考虑

#### 防重放攻击
- 使用递增的 nonce 值
- 在合约中记录已使用的 nonce
- 设置签名有效期（如 5 分钟）

#### 签名密钥管理
- 服务器使用专用的签名密钥对
- 合约存储对应的公钥
- 支持密钥轮换机制

#### 时间窗口控制
- 限制签名的有效时间窗口
- 防止过期签名被重复使用

### 5. 实现优势

1. **灵活性**: 可以实现更复杂的定价算法
2. **Gas 效率**: 减少链上计算，降低交易成本
3. **可扩展性**: 易于添加新的计算逻辑
4. **实时性**: 可以提供实时的价格和计算预览
5. **错误处理**: 更好的错误处理和用户体验

### 6. 向后兼容性

- 保持现有的 getter 方法
- 支持紧急情况下的链上计算回退
- 渐进式迁移策略

## 实现计划

1. **合约修改**: 添加签名验证逻辑
2. **服务器开发**: 实现计算和签名服务
3. **前端集成**: 集成新的购买流程
4. **测试覆盖**: 全面的单元测试和集成测试
5. **部署策略**: 安全的部署和迁移计划
