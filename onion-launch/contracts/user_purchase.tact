import "@stdlib/ownable";
import "@stdlib/deploy";

// Messages
message CreateUserPurchase {
    user: Address;
    amount: Int as coins;
    tokens: Int as coins;
    currency: Int as uint8; // 0=TON, 1=USDT
    purchase_method: Int as uint8; // 0=direct, 1=signature_verified
    nonce: Int as uint64; // For signature verification purchases
}

message Refund {
    purchase_id: Int as uint32;
}

message ProcessRefund {
    user: Address;
    amount: Int as coins;
    fee: Int as coins;
    currency: Int as uint8; // 0=TON, 1=USDT
}

// Structs
struct PurchaseRecord {
    id: Int as uint32;
    user: Address;
    amount: Int as coins;
    tokens: Int as coins;
    timestamp: Int as uint64;
    currency: Int as uint8;
    purchase_method: Int as uint8; // 0=direct, 1=signature_verified
    nonce: Int as uint64; // For signature verification purchases
}

// User Purchase Contract (Child Contract)
contract UserPurchase with Ownable {
    owner: Address;
    auction_address: Address;
    user_address: Address;
    total_purchased: Int as coins;
    total_paid: Int as coins;
    purchase_history: map<Int, PurchaseRecord>;
    refund_history: map<Int, Int>; // purchase_id -> refund_amount
    purchase_id_counter: Int as uint32;
    
    init(auction_address: Address, user_address: Address) {
        self.owner = user_address; // User owns their purchase contract
        self.auction_address = auction_address;
        self.user_address = user_address;
        self.total_purchased = 0;
        self.total_paid = 0;
        self.purchase_id_counter = 0;
    }
    
    
    // Deploy message handler
    receive("Deploy") {
        // Handle deployment - no special logic needed for this contract
    }
    
    // Create new purchase record
    receive(msg: CreateUserPurchase) {
        require(sender() == self.auction_address, "Unauthorized");

        self.purchase_id_counter += 1;
        let new_purchase: PurchaseRecord = PurchaseRecord{
            id: self.purchase_id_counter,
            user: msg.user,
            amount: msg.amount,
            tokens: msg.tokens,
            timestamp: now(),
            currency: msg.currency,
            purchase_method: msg.purchase_method,
            nonce: msg.nonce
        };

        self.purchase_history.set(self.purchase_id_counter, new_purchase);
        self.total_purchased += msg.tokens;
        self.total_paid += msg.amount;
    }
    
    // Request refund
    receive(msg: Refund) {
        require(sender() == self.user_address, "Unauthorized");
        
        let purchase: PurchaseRecord? = self.purchase_history.get(msg.purchase_id);
        require(purchase != null, "Purchase not found");
        require(self.refund_history.get(msg.purchase_id) == null, "Already refunded");
        
        let purchase_data: PurchaseRecord = purchase!!;
        
        // Mark as refunded
        self.refund_history.set(msg.purchase_id, purchase_data.amount);
        self.total_purchased -= purchase_data.tokens;
        self.total_paid -= purchase_data.amount;
        
        // Send refund request to auction contract
        send(SendParameters{
            to: self.auction_address,
            value: ton("0.05"),
            mode: SendIgnoreErrors,
            bounce: false,
            body: ProcessRefund{
                user: self.user_address,
                amount: purchase_data.amount,
                fee: (purchase_data.amount * 5) / 100,
                currency: purchase_data.currency
            }.toCell()
        });
    }
    
    // Getters
    get fun total_purchased(): Int {
        return self.total_purchased;
    }
    
    get fun total_paid(): Int {
        return self.total_paid;
    }
    
    get fun purchase_id_counter(): Int {
        return self.purchase_id_counter;
    }
    
    get fun purchase_details(purchase_id: Int): PurchaseRecord? {
        return self.purchase_history.get(purchase_id);
    }
    
    get fun is_refunded(purchase_id: Int): Bool {
        return self.refund_history.get(purchase_id) != null;
    }

    // New getters for signature verification support
    get fun signature_verified_purchases(): Int {
        let count: Int = 0;
        let i: Int = 1;
        while (i <= self.purchase_id_counter) {
            let purchase: PurchaseRecord? = self.purchase_history.get(i);
            if (purchase != null) {
                let record: PurchaseRecord = purchase!!;
                if (record.purchase_method == 1) {
                    count += 1;
                }
            }
            i += 1;
        }
        return count;
    }

    get fun purchase_method_stats(): map<Int, Int> {
        let stats: map<Int, Int> = emptyMap();
        let i: Int = 1;
        while (i <= self.purchase_id_counter) {
            let purchase: PurchaseRecord? = self.purchase_history.get(i);
            if (purchase != null) {
                let record: PurchaseRecord = purchase!!;
                let current_count: Int? = stats.get(record.purchase_method);
                if (current_count == null) {
                    stats.set(record.purchase_method, 1);
                } else {
                    stats.set(record.purchase_method, current_count!! + 1);
                }
            }
            i += 1;
        }
        return stats;
    }
}