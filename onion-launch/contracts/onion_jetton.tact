import "@stdlib/jetton";
import "@stdlib/ownable";
import "@stdlib/deploy";

// Onion Jetton Minter Contract
contract OnionJettonMinter with OwnableTransferrable, JettonMinter, Deployable {
    owner: Address;
    total_supply: Int as coins;
    mintable: Bool;
    content: Cell;
    auction_address: Address;

    init(owner: Address, auction_address: Address, content: Cell) {
        self.owner = owner;
        self.total_supply = 0;
        self.mintable = true;
        self.content = content;
        self.auction_address = auction_address;
    }

    // Deploy message handler
    receive(msg: Deploy) {
        // Handle deployment - no special logic needed for this contract
        self.notify("OnionJettonMinter deployed".asComment());
    }

    // Mint tokens (only callable by auction contract)
    receive(msg: JettonMint) {
        require(sender() == self.auction_address || sender() == self.owner, "Unauthorized mint");
        require(self.mintable, "Token not mintable");

        self.total_supply += msg.amount;
        
        let init: StateInit = self.getJettonWalletInit(msg.receiver);
        let walletAddress: Address = contractAddress(init);
        
        send(SendParameters{
            to: walletAddress,
            value: ton("0.1"),
            mode: SendIgnoreErrors,
            bounce: false,
            body: JettonInternalTransfer{
                query_id: msg.query_id,
                amount: msg.amount,
                from: myAddress(),
                response_destination: msg.response_destination,
                forward_ton_amount: msg.forward_ton_amount,
                forward_payload: msg.forward_payload
            }.toCell(),
            code: init.code,
            data: init.data
        });
    }

    // Burn tokens
    receive(msg: JettonBurnNotification) {
        self.requireOwner();
        self.total_supply -= msg.amount;
    }

    // Change admin
    receive(msg: JettonChangeAdmin) {
        self.requireOwner();
        self.owner = msg.new_admin;
    }

    // Change content
    receive(msg: JettonChangeContent) {
        self.requireOwner();
        self.content = msg.new_content;
    }

    get fun get_jetton_data(): JettonData {
        return JettonData{
            total_supply: self.total_supply,
            mintable: self.mintable,
            admin_address: self.owner,
            jetton_content: self.content,
            jetton_wallet_code: self.getJettonWalletInit(self.owner).code
        };
    }

    get fun get_wallet_address(owner_address: Address): Address {
        return contractAddress(self.getJettonWalletInit(owner_address));
    }

    fun getJettonWalletInit(address: Address): StateInit {
        return initOf OnionJettonWallet(myAddress(), address);
    }
}

// Onion Jetton Wallet Contract
contract OnionJettonWallet with JettonWallet, Deployable {
    master: Address;
    owner: Address;
    balance: Int as coins;

    init(master: Address, owner: Address) {
        self.master = master;
        self.owner = owner;
        self.balance = 0;
    }

    // Deploy message handler
    receive(msg: Deploy) {
        // Handle deployment - no special logic needed for this contract
        self.notify("OnionJettonWallet deployed".asComment());
    }

    // Internal transfer (mint/receive)
    receive(msg: JettonInternalTransfer) {
        let ctx: Context = context();
        
        if (msg.from == self.master) {
            // Minting case
            self.balance += msg.amount;
        } else {
            // Transfer case
            let walletInitCode: StateInit = self.getJettonWalletInit(msg.from);
            require(contractAddress(walletInitCode) == ctx.sender, "Invalid sender");
            self.balance += msg.amount;
        }

        // Send excess back
        if (msg.forward_ton_amount > 0) {
            send(SendParameters{
                to: self.owner,
                value: msg.forward_ton_amount,
                mode: SendPayGasSeparately + SendIgnoreErrors,
                body: msg.forward_payload
            });
        }

        // Notify sender about successful transfer
        if (msg.response_destination != null) {
            send(SendParameters{
                to: msg.response_destination!!,
                value: 0,
                mode: SendRemainingValue + SendIgnoreErrors,
                body: JettonExcesses{query_id: msg.query_id}.toCell()
            });
        }
    }

    // Transfer tokens
    receive(msg: JettonTransfer) {
        let ctx: Context = context();
        require(ctx.sender == self.owner, "Unauthorized transfer");
        require(self.balance >= msg.amount, "Insufficient balance");

        self.balance -= msg.amount;

        let walletInitCode: StateInit = self.getJettonWalletInit(msg.destination);
        let walletAddress: Address = contractAddress(walletInitCode);

        send(SendParameters{
            to: walletAddress,
            value: msg.forward_ton_amount + ton("0.1"),
            mode: SendPayGasSeparately,
            bounce: true,
            body: JettonInternalTransfer{
                query_id: msg.query_id,
                amount: msg.amount,
                from: self.owner,
                response_destination: msg.response_destination,
                forward_ton_amount: msg.forward_ton_amount,
                forward_payload: msg.forward_payload
            }.toCell(),
            code: walletInitCode.code,
            data: walletInitCode.data
        });
    }

    // Burn tokens
    receive(msg: JettonBurn) {
        let ctx: Context = context();
        require(ctx.sender == self.owner, "Unauthorized burn");
        require(self.balance >= msg.amount, "Insufficient balance");

        self.balance -= msg.amount;

        send(SendParameters{
            to: self.master,
            value: 0,
            mode: SendRemainingValue + SendIgnoreErrors,
            body: JettonBurnNotification{
                query_id: msg.query_id,
                amount: msg.amount,
                sender: self.owner,
                response_destination: msg.response_destination
            }.toCell()
        });
    }

    get fun get_wallet_data(): JettonWalletData {
        return JettonWalletData{
            balance: self.balance,
            owner: self.owner,
            master: self.master,
            code: myCode()
        };
    }

    fun getJettonWalletInit(address: Address): StateInit {
        return initOf OnionJettonWallet(self.master, address);
    }
}